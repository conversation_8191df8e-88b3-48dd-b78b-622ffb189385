use super::service_trait::{
    PermissionManagementTrait, PermissionQueryTrait, PermissionServiceTrait,
};
use crate::database::Database;
use crate::errors::Result;
use crate::modules::permission::models::{
    CreatePermissionRequest, PaginatedPermissions, Permission, PermissionPaginationRequest,
    UpdatePermission, UpdatePermissionRequest,
};
use crate::modules::permission::repository::{DieselPermissionRepository, DynPermissionRepo};
use crate::utils::pagination::PaginationRequest;
use crate::utils::validation::helpers::validate_enhanced_and_execute;
use crate::utils::ErrorHelper;
use async_trait::async_trait;
use chrono::Utc;
use std::sync::Arc;
use uuid::Uuid;

#[derive(Clone)]
pub struct PermissionService {
    repository: DynPermissionRepo,
}

impl PermissionService {
    pub fn new(database: Database) -> Self {
        Self {
            repository: Arc::new(DieselPermissionRepository::new(database)),
        }
    }
}

#[async_trait]
impl PermissionQueryTrait for PermissionService {
    async fn get_permission_by_id(&self, id: &Uuid) -> Result<Permission> {
        let permission = self
            .repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| ErrorHelper::not_found("Permission", Some(&id.to_string())))?;

        Ok(permission)
    }

    async fn get_permission_by_name(&self, name: &str) -> Result<Permission> {
        let permission = self
            .repository
            .find_by_name(name)
            .await?
            .ok_or_else(|| ErrorHelper::not_found_by_name("Permission", name))?;

        Ok(permission)
    }

    async fn get_permissions_by_ids(&self, ids: &[Uuid]) -> Result<Vec<Permission>> {
        self.repository.find_by_ids(ids).await
    }

    async fn get_permissions_by_names(&self, names: &[String]) -> Result<Vec<Permission>> {
        self.repository.find_by_names(names).await
    }

    async fn get_permissions(
        &self,
        pagination: PermissionPaginationRequest,
    ) -> Result<PaginatedPermissions> {
        let (permissions, total) = self.repository.find_all_with_pagination(&pagination).await?;

        let pagination_meta = crate::utils::pagination::PaginationMeta::new(
            pagination.page(),
            pagination.limit(),
            total,
        );

        Ok(PaginatedPermissions {
            permissions,
            meta: pagination_meta.into(),
        })
    }

    async fn permission_exists_by_name(&self, name: &str) -> Result<bool> {
        self.repository.exists_by_name(name).await
    }
}

#[async_trait]
impl PermissionManagementTrait for PermissionService {
    async fn create_permission(&self, request: CreatePermissionRequest) -> Result<Permission> {
        validate_enhanced_and_execute(request, |req| async move {
            // Check if permission already exists
            if self.repository.exists_by_name(&req.name).await? {
                return Err(ErrorHelper::conflict("Permission", "name", &req.name));
            }

            let permission = Permission {
                id: Uuid::new_v4(),
                name: req.name.clone(),
                description: req.description.clone(),
                created_at: Utc::now(),
                updated_at: Utc::now(),
            };

            self.repository.create(&permission).await
        })
        .await
    }

    async fn create_permission_direct(&self, permission: Permission) -> Result<Permission> {
        // Check if permission already exists
        if self.repository.exists_by_name(&permission.name).await? {
            return Err(ErrorHelper::conflict("Permission", "name", &permission.name));
        }

        self.repository.create(&permission).await
    }

        async fn update_permission(
        &self,
        id: &Uuid,
        request: UpdatePermissionRequest,
    ) -> Result<Permission> {
        validate_enhanced_and_execute(request, |req| async move {
            // Check if permission exists
            let _existing = self.get_permission_by_id(id).await?;

            // Check name conflict if name is being updated
            if let Some(ref new_name) = req.name {
                if self
                    .repository
                    .exists_by_name_exclude_id(new_name, id)
                    .await?
                {
                     return Err(ErrorHelper::conflict("Permission", "name", new_name));
                 }
            }

            let update_data = UpdatePermission {
                name: req.name,
                description: req.description,
                updated_at: Utc::now(),
            };

            self.repository
                .update(id, update_data)
                .await?
                .ok_or_else(|| ErrorHelper::not_found("Permission", Some(&id.to_string())))
        })
        .await
    }

    async fn delete_permission(&self, id: &Uuid) -> Result<()> {
        // Check if permission exists
        let _existing = self.get_permission_by_id(id).await?;

        let deleted = self.repository.delete(id).await?;

        if !deleted {
            return Err(ErrorHelper::not_found("Permission", Some(&id.to_string())));
        }

        Ok(())
    }
}

// Implement the combined trait
impl PermissionServiceTrait for PermissionService {}
