use crate::schema::permissions;
use crate::utils::pagination::{PaginationMeta, PaginationRequest};
use crate::utils::validation::{
    validate_required_string, validate_string_length, ValidateRequestEnhanced, ValidationResult,
};
use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use utoipa::{schema, ToSchema};
use uuid::Uuid;
use validator::Validate;
// ===== DOMAIN MODELS =====
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "name": "users:read",
    "description": "Allows reading user information",
    "created_at": "2025-07-05T01:31:59.286900Z",
    "updated_at": "2025-07-05T01:31:59.286900Z"
}))]
pub struct Permission {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct CreatePermissionRequest {
    #[validate(length(
        min = 2,
        max = 100,
        message = "Permission name must be between 2 and 100 characters"
    ))]
    pub name: String,
    #[validate(length(max = 255, message = "Description must not exceed 255 characters"))]
    pub description: Option<String>,
}

impl ValidateRequestEnhanced for CreatePermissionRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut errors = ValidationResult::new();

        validate_required_string(&self.name, "name", &mut errors);
        if !self.name.trim().is_empty() {
            validate_string_length(&self.name, "name", 2, 100, &mut errors);
        }

        if let Some(ref desc) = self.description {
            validate_string_length(desc, "description", 0, 255, &mut errors);
        }

        errors
    }
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UpdatePermissionRequest {
    #[validate(length(
        min = 2,
        max = 100,
        message = "Permission name must be between 2 and 100 characters"
    ))]
    pub name: Option<String>,
    #[validate(length(max = 255, message = "Description must not exceed 255 characters"))]
    pub description: Option<Option<String>>,
}

impl ValidateRequestEnhanced for UpdatePermissionRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut errors = ValidationResult::new();

        if let Some(ref name) = self.name {
            validate_required_string(name, "name", &mut errors);
            if !name.trim().is_empty() {
                validate_string_length(name, "name", 2, 100, &mut errors);
            }
        }

        if let Some(Some(ref desc)) = self.description {
            validate_string_length(desc, "description", 0, 255, &mut errors);
        }

        errors
    }
}

// ===== PAGINATION MODELS =====
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct PermissionPaginationRequest {
    #[serde(default = "default_page")]
    #[validate(range(min = 1, message = "Page must be at least 1"))]
    pub page: i64,
    #[serde(default = "default_limit")]
    #[validate(range(min = 1, max = 100, message = "Limit must be between 1 and 100"))]
    pub limit: i64,
}

impl PaginationRequest for PermissionPaginationRequest {
    fn page(&self) -> i64 {
        self.page
    }

    fn limit(&self) -> i64 {
        self.limit
    }
}

fn default_page() -> i64 {
    1
}
fn default_limit() -> i64 {
    10
}

// Create a wrapper struct for ToSchema compatibility
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "page": 1,
    "limit": 10,
    "total": 20,
    "total_pages": 2
}))]
pub struct PermissionPagination {
    pub page: i64,
    pub limit: i64,
    pub total: i64,
    pub total_pages: i64,
}

impl From<PaginationMeta> for PermissionPagination {
    fn from(meta: PaginationMeta) -> Self {
        Self {
            page: meta.page,
            limit: meta.limit,
            total: meta.total,
            total_pages: meta.total_pages,
        }
    }
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "permissions": [
        {
            "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
            "name": "users:read",
            "description": "Allows reading user information",
            "created_at": "2025-07-05T01:31:59.286900Z",
            "updated_at": "2025-07-05T01:31:59.286900Z"
        },
        {
            "id": "b2c3d4e5-f6a7-8901-2345-67890abcdef0",
            "name": "users:write",
            "description": "Allows creating and updating user information",
            "created_at": "2025-07-05T01:32:59.286900Z",
            "updated_at": "2025-07-05T01:32:59.286900Z"
        }
    ],
    "meta": {
        "page": 1,
        "limit": 10,
        "total": 2,
        "total_pages": 1
    }
}))]
pub struct PaginatedPermissions {
    pub permissions: Vec<Permission>,
    pub meta: PermissionPagination,
}

// ===== DIESEL MODELS =====
#[derive(Debug, Queryable, Selectable)]
#[diesel(table_name = permissions)]
pub struct DieselPermission {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = permissions)]
pub struct NewPermission {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
}

#[derive(Debug, AsChangeset)]
#[diesel(table_name = permissions)]
pub struct UpdatePermission {
    pub name: Option<String>,
    pub description: Option<Option<String>>,
    pub updated_at: DateTime<Utc>,
}

// ===== IMPLEMENTATION =====
impl Permission {
    pub fn new(name: String, description: Option<String>) -> Self {
        Self {
            id: Uuid::new_v4(),
            name,
            description,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }
}

// ===== FROM TRAIT IMPLEMENTATIONS =====
impl From<DieselPermission> for Permission {
    fn from(diesel_permission: DieselPermission) -> Self {
        Self {
            id: diesel_permission.id,
            name: diesel_permission.name,
            description: diesel_permission.description,
            created_at: diesel_permission.created_at,
            updated_at: diesel_permission.updated_at,
        }
    }
}
