use crate::modules::permission::models::Permission;
use crate::schema::roles;
use crate::utils::pagination::{PaginationMeta, PaginationRequest};
use crate::utils::validation::{
    validate_required_string, validate_string_length, ValidateRequestEnhanced, ValidationResult,
};
use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use uuid::Uuid;
use validator::Validate;

// ===== DOMAIN MODELS =====
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "name": "editor",
    "description": "Can edit content, but not manage users.",
    "created_at": "2025-07-05T01:31:59.286900Z",
    "updated_at": "2025-07-05T01:31:59.286900Z"
}))]
pub struct Role {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "name": "editor",
    "description": "Can edit content, but not manage users.",
    "permissions": [
        {
            "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
            "name": "posts:read",
            "description": "Can read posts",
            "created_at": "2025-07-05T01:31:59.286900Z",
            "updated_at": "2025-07-05T01:31:59.286900Z"
        },
        {
            "id": "b2c3d4e5-f6a7-8901-2345-67890abcdef0",
            "name": "posts:write",
            "description": "Can write posts",
            "created_at": "2025-07-05T01:31:59.286900Z",
            "updated_at": "2025-07-05T01:31:59.286900Z"
        }
    ],
    "user_count": 5,
    "created_at": "2025-07-05T01:31:59.286900Z",
    "updated_at": "2025-07-05T01:31:59.286900Z"
}))]
pub struct RoleWithPermissions {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub permissions: Vec<Permission>,
    pub user_count: i64,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct CreateRoleRequest {
    #[validate(length(
        min = 2,
        max = 50,
        message = "Role name must be between 2 and 50 characters"
    ))]
    pub name: String,

    #[validate(length(max = 200, message = "Description must not exceed 200 characters"))]
    pub description: Option<String>,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UpdateRoleRequest {
    #[validate(length(
        min = 2,
        max = 50,
        message = "Role name must be between 2 and 50 characters"
    ))]
    pub name: Option<String>,

    #[validate(length(max = 200, message = "Description must not exceed 200 characters"))]
    pub description: Option<Option<String>>,
}

// ===== PAGINATION MODELS =====
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct RolePaginationRequest {
    #[serde(default = "default_page")]
    #[validate(range(min = 1, message = "Page must be at least 1"))]
    pub page: i64,
    #[serde(default = "default_limit")]
    #[validate(range(min = 1, max = 100, message = "Limit must be between 1 and 100"))]
    pub limit: i64,
    pub name: Option<String>,
}

impl PaginationRequest for RolePaginationRequest {
    fn page(&self) -> i64 {
        self.page
    }
    fn limit(&self) -> i64 {
        self.limit
    }
}

fn default_page() -> i64 {
    1
}
fn default_limit() -> i64 {
    10
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "page": 1,
    "limit": 10,
    "total": 5,
    "total_pages": 1
}))]
pub struct RolePagination {
    pub page: i64,
    pub limit: i64,
    pub total: i64,
    pub total_pages: i64,
}

impl From<PaginationMeta> for RolePagination {
    fn from(meta: PaginationMeta) -> Self {
        Self {
            page: meta.page,
            limit: meta.limit,
            total: meta.total,
            total_pages: meta.total_pages,
        }
    }
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "roles": [
        {
            "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
            "name": "editor",
            "description": "Can edit content, but not manage users.",
            "created_at": "2025-07-05T01:31:59.286900Z",
            "updated_at": "2025-07-05T01:31:59.286900Z"
        },
        {
            "id": "e2f3a4b5-c6d7-8901-2345-67890abcdef0",
            "name": "viewer",
            "description": "Can only view content.",
            "created_at": "2025-07-05T01:32:59.286900Z",
            "updated_at": "2025-07-05T01:32:59.286900Z"
        }
    ],
    "meta": {
        "page": 1,
        "limit": 10,
        "total": 2,
        "total_pages": 1
    }
}))]
pub struct PaginatedRoles {
    pub roles: Vec<Role>,
    pub meta: RolePagination,
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "roles": [
        {
            "id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
            "name": "editor",
            "description": "Can edit content, but not manage users.",
                         "permissions": [
                 {
                     "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
                     "name": "posts:read",
                     "description": "Can read posts",
                     "created_at": "2025-07-05T01:31:59.286900Z",
                     "updated_at": "2025-07-05T01:31:59.286900Z"
                 },
                 {
                     "id": "b2c3d4e5-f6a7-8901-2345-67890abcdef0",
                     "name": "posts:write",
                     "description": "Can write posts",
                     "created_at": "2025-07-05T01:31:59.286900Z",
                     "updated_at": "2025-07-05T01:31:59.286900Z"
                 }
             ],
             "user_count": 5,
             "created_at": "2025-07-05T01:31:59.286900Z",
             "updated_at": "2025-07-05T01:31:59.286900Z"
         },
         {
             "id": "e2f3a4b5-c6d7-8901-2345-67890abcdef0",
             "name": "viewer",
             "description": "Can only view content.",
             "permissions": [],
             "user_count": 2,
             "created_at": "2025-07-05T01:32:59.286900Z",
             "updated_at": "2025-07-05T01:32:59.286900Z"
        }
    ],
    "meta": {
        "page": 1,
        "limit": 10,
        "total": 2,
        "total_pages": 1
    }
}))]
pub struct PaginatedRolesWithPermissions {
    pub roles: Vec<RoleWithPermissions>,
    pub meta: RolePagination,
}

#[derive(Debug, Deserialize, ToSchema)]
pub struct ReplaceRolePermissionsRequest {
    pub permission_ids: Vec<Uuid>,
}

// ===== PERMISSION MATRIX BULK UPDATE MODELS =====
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "changes": [
        {
            "role_id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
            "permission_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
            "granted": true
        },
        {
            "role_id": "d1e2f3a4-b5c6-7890-1234-567890abcdef", 
            "permission_id": "b2c3d4e5-f6a7-8901-2345-67890abcdef0",
            "granted": false
        }
    ]
}))]
pub struct PermissionMatrixUpdateRequest {
    #[validate(length(
        min = 1,
        max = 100,
        message = "Changes must be between 1 and 100 items"
    ))]
    pub changes: Vec<PermissionMatrixChange>,
}

#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "role_id": "d1e2f3a4-b5c6-7890-1234-567890abcdef",
    "permission_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef", 
    "granted": true
}))]
pub struct PermissionMatrixChange {
    pub role_id: Uuid,
    pub permission_id: Uuid,
    pub granted: bool,
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "changes_applied": 5,
    "roles_affected": ["admin", "editor"],
    "permissions_affected": ["users:read", "users:write", "roles:read"]
}))]
pub struct PermissionMatrixUpdateResponse {
    pub changes_applied: i32,
    pub roles_affected: Vec<String>,
    pub permissions_affected: Vec<String>,
}

// ===== DIESEL DATABASE MODELS =====
#[derive(Debug, Clone, Queryable, Selectable, Serialize, Deserialize)]
#[diesel(table_name = roles)]
pub struct DieselRole {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Insertable)]
#[diesel(table_name = roles)]
pub struct NewRole {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
}

#[derive(Debug, Clone, AsChangeset)]
#[diesel(table_name = roles)]
pub struct UpdateRole {
    pub name: Option<String>,
    pub description: Option<Option<String>>,
    pub updated_at: DateTime<Utc>,
}

// ===== IMPLEMENTATIONS =====
impl Role {
    pub fn new(name: String, description: Option<String>) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            name,
            description,
            created_at: now,
            updated_at: now,
        }
    }
}

impl From<DieselRole> for Role {
    fn from(model: DieselRole) -> Self {
        Self {
            id: model.id,
            name: model.name,
            description: model.description,
            created_at: model.created_at,
            updated_at: model.updated_at,
        }
    }
}

impl RoleWithPermissions {
    pub fn from_role_and_permissions(
        role: Role,
        permissions: Vec<Permission>,
        user_count: i64,
    ) -> Self {
        Self {
            id: role.id,
            name: role.name,
            description: role.description,
            permissions,
            user_count,
            created_at: role.created_at,
            updated_at: role.updated_at,
        }
    }
}

// ===== ENHANCED VALIDATION =====
impl ValidateRequestEnhanced for CreateRoleRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut errors = ValidationResult::new();

        // Validate name
        validate_required_string(&self.name, "name", &mut errors);
        if !self.name.trim().is_empty() {
            validate_string_length(&self.name, "name", 2, 50, &mut errors);
        }

        // Validate description if provided
        if let Some(ref desc) = self.description {
            validate_string_length(desc, "description", 5, 200, &mut errors);
        }

        errors
    }
}

impl ValidateRequestEnhanced for UpdateRoleRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut errors = ValidationResult::new();

        // Validate name if provided
        if let Some(ref name) = self.name {
            validate_required_string(name, "name", &mut errors);
            if !name.trim().is_empty() {
                validate_string_length(name, "name", 2, 50, &mut errors);
            }
        }

        // Validate description if provided
        if let Some(Some(ref desc)) = self.description {
            validate_string_length(desc, "description", 5, 200, &mut errors);
        }

        errors
    }
}

impl ValidateRequestEnhanced for PermissionMatrixUpdateRequest {
    fn validate_enhanced(&self) -> ValidationResult {
        let mut errors = ValidationResult::new();

        // Validate changes vector
        if self.changes.is_empty() {
            errors.add_error(
                "changes",
                "CHANGES_REQUIRED",
                "Changes list cannot be empty",
            );
        } else if self.changes.len() > 100 {
            errors.add_error(
                "changes",
                "CHANGES_TOO_MANY",
                "Changes list cannot exceed 100 items",
            );
        }

        // Check for duplicate changes (same role_id + permission_id combination)
        let mut seen_combinations = std::collections::HashSet::new();
        for (index, change) in self.changes.iter().enumerate() {
            let combination = (change.role_id, change.permission_id);
            if seen_combinations.contains(&combination) {
                errors.add_error(
                    &format!("changes[{}]", index),
                    "DUPLICATE_CHANGE",
                    &format!(
                        "Duplicate change detected for role_id: {}, permission_id: {}",
                        change.role_id, change.permission_id
                    ),
                );
            } else {
                seen_combinations.insert(combination);
            }
        }

        errors
    }
}
